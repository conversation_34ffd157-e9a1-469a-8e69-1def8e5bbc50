#!/bin/bash

# 🔧 إصلاح مشكلة الميكروفون على مستوى الأجهزة
echo "🔧 إصلاح مشكلة الميكروفون على مستوى الأجهزة"
echo "=============================================="
echo ""

# 1. فحص جميع أجهزة الصوت المتاحة
echo "📱 فحص جميع أجهزة الصوت المتاحة..."
echo "=================================="
system_profiler SPAudioDataType

echo ""
echo "🔍 الأجهزة المتاحة للإدخال:"
system_profiler SPAudioDataType | grep -B 5 -A 5 "Input Channels"

echo ""

# 2. فحص اتصال USB
echo "🔌 فحص اتصال USB..."
echo "=================="
system_profiler SPUSBDataType | grep -A 10 -B 5 "Audio"

echo ""

# 3. إعادة تعيين أجهزة الصوت
echo "🔄 إعادة تعيين أجهزة الصوت..."
echo "============================"

# إيقاف خدمة الصوت
echo "🛑 إيقاف خدمة الصوت..."
sudo killall coreaudiod

# انتظار
echo "⏳ انتظار 5 ثوان..."
sleep 5

# إعادة تشغيل خدمة الصوت
echo "🔄 إعادة تشغيل خدمة الصوت..."
# الخدمة ستعيد تشغيل نفسها تلقائياً

# 4. تجربة تبديل أجهزة الإدخال
echo "🔀 تجربة تبديل أجهزة الإدخال..."
echo "=============================="

# فتح إعدادات الصوت
echo "⚙️ فتح إعدادات الصوت..."
open "/System/Preferences/Sound.prefPane"

echo "✅ تم فتح إعدادات الصوت"
echo ""

# 5. اختبار مع الميكروفون المدمج
echo "🎤 اختبار مع الميكروفون المدمج..."
echo "==============================="

# محاولة التبديل للميكروفون المدمج
osascript << 'EOF'
tell application "System Preferences"
    activate
    set current pane to pane "com.apple.preference.sound"
    delay 2
end tell

tell application "System Events"
    tell process "System Preferences"
        click button "Input" of tab group 1 of window "Sound"
        delay 1
        -- محاولة اختيار الميكروفون المدمج
        try
            click (first row of table 1 of scroll area 1 of tab group 1 of window "Sound" whose value of text field 1 contains "Built-in")
        end try
    end tell
end tell
EOF

echo "🔄 تم محاولة التبديل للميكروفون المدمج"
echo ""

# 6. اختبار سريع
echo "🧪 اختبار سريع للميكروفون..."
echo "============================"

# رفع مستوى الصوت
osascript -e "set volume input volume 100"

# اختبار مع تطبيق Voice Memos
echo "🎙️ فتح تطبيق Voice Memos للاختبار..."
open -a "Voice Memos"

echo ""

# 7. تعليمات مفصلة
echo "📝 تعليمات مفصلة للإصلاح:"
echo "=========================="
echo ""
echo "🔧 في نافذة إعدادات الصوت المفتوحة:"
echo "   1. انقر على تبويب 'Input'"
echo "   2. جرب تبديل بين الأجهزة المتاحة:"
echo "      - USB Audio Device (الحالي)"
echo "      - Built-in Microphone (المدمج)"
echo "      - Built-in Line Input"
echo "   3. تحدث وراقب شريط 'Input level'"
echo "   4. إذا تحرك الشريط، فالميكروفون يعمل"
echo ""

echo "🎙️ في تطبيق Voice Memos:"
echo "   1. انقر على زر التسجيل الأحمر"
echo "   2. تحدث لبضع ثوان"
echo "   3. أوقف التسجيل"
echo "   4. شغل التسجيل للتأكد"
echo ""

echo "🔌 فحص الاتصال الفيزيائي:"
echo "   1. تأكد من أن كابل USB متصل بإحكام"
echo "   2. جرب منفذ USB مختلف"
echo "   3. تأكد من أن الميكروفون لا يحتاج تشغيل منفصل"
echo "   4. تحقق من وجود زر كتم على الميكروفون نفسه"
echo ""

# 8. إنشاء سكريبت تبديل سريع
echo "🚀 إنشاء سكريبت تبديل سريع..."
echo "============================"

cat > switch_microphone.sh << 'EOF'
#!/bin/bash
echo "🔀 تبديل الميكروفون"
echo "=================="
echo "1. USB Audio Device"
echo "2. Built-in Microphone"
echo "3. عرض الحالة الحالية"
echo ""
read -p "اختر (1-3): " choice

case $choice in
    1)
        echo "🔄 التبديل إلى USB Audio Device..."
        # سيتم التبديل عبر إعدادات النظام
        ;;
    2)
        echo "🔄 التبديل إلى Built-in Microphone..."
        # سيتم التبديل عبر إعدادات النظام
        ;;
    3)
        echo "📊 الحالة الحالية:"
        system_profiler SPAudioDataType | grep -A 5 "Default Input Device: Yes"
        osascript -e "get volume settings"
        ;;
esac
EOF

chmod +x switch_microphone.sh

echo "✅ تم إنشاء سكريبت التبديل السريع"
echo ""

# 9. نصائح إضافية
echo "💡 نصائح إضافية:"
echo "================"
echo "1. إذا كان USB Audio Device لا يعمل، جرب الميكروفون المدمج"
echo "2. تأكد من عدم وجود تطبيقات أخرى تستخدم الميكروفون"
echo "3. أعد تشغيل الجهاز إذا استمرت المشكلة"
echo "4. تحقق من تحديثات النظام"
echo ""

echo "🎯 الهدف: رؤية حركة في شريط 'Input level' عند التحدث"
echo ""
echo "🎉 انتهى إصلاح الأجهزة!"

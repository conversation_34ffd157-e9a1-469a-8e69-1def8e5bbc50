#!/bin/bash
echo "🔀 تبديل الميكروفون"
echo "=================="
echo "1. USB Audio Device"
echo "2. Built-in Microphone"
echo "3. عرض الحالة الحالية"
echo ""
read -p "اختر (1-3): " choice

case $choice in
    1)
        echo "🔄 التبديل إلى USB Audio Device..."
        # سيتم التبديل عبر إعدادات النظام
        ;;
    2)
        echo "🔄 التبديل إلى Built-in Microphone..."
        # سيتم التبديل عبر إعدادات النظام
        ;;
    3)
        echo "📊 الحالة الحالية:"
        system_profiler SPAudioDataType | grep -A 5 "Default Input Device: Yes"
        osascript -e "get volume settings"
        ;;
esac

#!/bin/bash

# 🎤 اختبار الميكروفون المدمج
echo "🎤 اختبار الميكروفون المدمج"
echo "=========================="
echo ""

# 1. فحص الأجهزة المتاحة
echo "📱 الأجهزة المتاحة للإدخال:"
echo "=========================="
system_profiler SPAudioDataType | grep -B 2 -A 5 "Input Channels"

echo ""

# 2. محاولة التبديل للميكروفون المدمج
echo "🔄 محاولة استخدام الميكروفون المدمج..."
echo "===================================="

# إنشاء سكريبت AppleScript لتغيير جهاز الإدخال
osascript << 'EOF'
try
    tell application "System Events"
        -- فتح إعدادات الصوت
        do shell script "open '/System/Library/PreferencePanes/Sound.prefPane'"
        delay 3
        
        tell process "System Preferences"
            -- النقر على تبويب Input
            try
                click button "Input" of tab group 1 of window 1
                delay 2
                
                -- البحث عن الميكروفون المدمج
                set deviceList to every row of table 1 of scroll area 1 of tab group 1 of window 1
                repeat with deviceRow in deviceList
                    set deviceName to value of text field 1 of deviceRow
                    if deviceName contains "Built-in" or deviceName contains "Internal" then
                        click deviceRow
                        delay 1
                        exit repeat
                    end if
                end repeat
                
            on error
                -- إذا فشل، جرب طريقة أخرى
                log "فشل في تغيير الجهاز"
            end try
        end tell
    end tell
    
    return "✅ تم محاولة التبديل للميكروفون المدمج"
    
on error errMsg
    return "❌ فشل: " & errMsg
end try
EOF

echo ""

# 3. فحص الجهاز الحالي
echo "🔍 فحص الجهاز الحالي..."
echo "===================="
system_profiler SPAudioDataType | grep -A 5 "Default Input Device: Yes"

echo ""

# 4. اختبار مع تطبيق مدمج
echo "🧪 اختبار مع تطبيق مدمج..."
echo "========================"

# محاولة فتح تطبيق التسجيل
echo "🎙️ محاولة فتح تطبيق التسجيل..."

# جرب QuickTime Player
osascript << 'EOF'
try
    tell application "QuickTime Player"
        activate
        delay 1
        new audio recording
        delay 2
    end tell
    return "✅ تم فتح QuickTime Player للتسجيل"
on error
    return "❌ فشل في فتح QuickTime Player"
end try
EOF

echo ""

# 5. اختبار مستوى الصوت
echo "📊 اختبار مستوى الصوت..."
echo "======================"

echo "🔊 المستوى الحالي:"
osascript -e "get volume settings"

# رفع مستوى الميكروفون
echo "📈 رفع مستوى الميكروفون..."
osascript -e "set volume input volume 100"

echo "🔊 المستوى الجديد:"
osascript -e "get volume settings"

echo ""

# 6. تعليمات الاختبار
echo "📝 تعليمات الاختبار:"
echo "==================="
echo ""
echo "🎤 في QuickTime Player (إذا فتح):"
echo "   1. انقر على الزر الأحمر للتسجيل"
echo "   2. تحدث بوضوح لمدة 5 ثوان"
echo "   3. أوقف التسجيل"
echo "   4. شغل التسجيل للتأكد من وجود صوت"
echo ""

echo "⚙️ في إعدادات الصوت:"
echo "   1. ابحث عن تبويب 'Input' أو 'إدخال'"
echo "   2. جرب تبديل بين:"
echo "      - USB Audio Device"
echo "      - Built-in Line Input"
echo "      - أي جهاز آخر متاح"
echo "   3. راقب شريط 'Input Level' أثناء التحدث"
echo ""

echo "🔧 إذا لم يعمل أي شيء:"
echo "   1. تأكد من أن الميكروفون USB متصل بإحكام"
echo "   2. جرب منفذ USB مختلف"
echo "   3. تحقق من وجود زر كتم على الميكروفون"
echo "   4. أعد تشغيل الجهاز"
echo ""

# 7. إنشاء اختبار سريع
echo "🚀 إنشاء اختبار سريع..."
echo "===================="

cat > quick_mic_test.sh << 'EOF'
#!/bin/bash
echo "🎤 اختبار سريع للميكروفون"
echo "=========================="

echo "📊 الحالة الحالية:"
osascript -e "get volume settings"
echo ""

echo "📱 الجهاز الافتراضي:"
system_profiler SPAudioDataType | grep -A 3 "Default Input Device: Yes"
echo ""

echo "🧪 فتح اختبار الميكروفون..."
open "https://mictests.com/"

echo "✅ تم فتح صفحة الاختبار"
echo "📝 اختبر الميكروفون في المتصفح"
EOF

chmod +x quick_mic_test.sh

echo "✅ تم إنشاء اختبار سريع (quick_mic_test.sh)"
echo ""

echo "🎯 الخطوة التالية:"
echo "================"
echo "1. اختبر الميكروفون في QuickTime Player أولاً"
echo "2. إذا عمل، فالمشكلة في المتصفح"
echo "3. إذا لم يعمل، فالمشكلة في الجهاز نفسه"
echo ""

echo "🎉 انتهى اختبار الميكروفون المدمج!"
